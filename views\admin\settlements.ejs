<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('admin_referral.title') %></h1>
      <p class="text-gray-400 mt-1"><%= t('admin_referral.subtitle') %></p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="refreshPage()" class="btn-secondary">
        <i class="ti ti-refresh mr-2"></i>
        <%= t('admin_referral.refresh') %>
      </button>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
  <!-- Total Referrals -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-users text-blue-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('admin_referral.total_referrals') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= referralStats.total_referrals || 0 %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('admin_referral.total_referrals_desc') %></p>
  </div>

  <!-- Completed Referrals -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-check text-green-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('admin_referral.completed_referrals') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      <%= referralStats.completed_referrals || 0 %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('admin_referral.completed_referrals_desc') %></p>
  </div>

  <!-- Total Commissions -->
  <div class="card-enhanced p-6">
    <div class="flex items-center justify-between mb-4">
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-coins text-yellow-400 text-xl"></i>
      </div>
      <span class="text-sm text-gray-400"><%= t('admin_referral.total_commissions') %></span>
    </div>
    <div class="text-2xl font-bold text-white mb-1">
      Rp <%= (referralStats.total_commissions || 0).toLocaleString('id-ID') %>
    </div>
    <p class="text-gray-400 text-sm"><%= t('admin_referral.total_commissions_desc') %></p>
  </div>
</div>

<!-- Withdrawal Requests -->
<div class="card-enhanced p-6">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-white"><%= t('admin_referral.withdrawal_requests') %></h3>
    <div class="flex items-center space-x-2">
      <select id="statusFilter" onchange="filterRequests()" class="bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm">
        <option value=""><%= t('admin_referral.all_status') %></option>
        <option value="pending"><%= t('admin_referral.status.pending') %></option>
        <option value="approved"><%= t('admin_referral.status.approved') %></option>
        <option value="rejected"><%= t('admin_referral.status.rejected') %></option>
      </select>
    </div>
  </div>

  <% if (withdrawalRequests && withdrawalRequests.length > 0) { %>
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-700">
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.user') %></th>
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.amount') %></th>
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.bank') %></th>
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.account') %></th>
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.status') %></th>
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.date') %></th>
            <th class="text-left py-3 px-4 text-gray-300 font-medium"><%= t('admin_referral.actions') %></th>
          </tr>
        </thead>
        <tbody id="withdrawalTableBody">
          <% withdrawalRequests.forEach(request => { %>
            <tr class="border-b border-gray-800 withdrawal-row" data-status="<%= request.status %>">
              <td class="py-4 px-4">
                <div>
                  <p class="text-white font-medium"><%= request.username %></p>
                  <p class="text-gray-400 text-sm"><%= request.email || t('admin_referral.no_email') %></p>
                </div>
              </td>
              <td class="py-4 px-4">
                <p class="text-white font-semibold">Rp <%= request.amount.toLocaleString('id-ID') %></p>
              </td>
              <td class="py-4 px-4">
                <p class="text-white"><%= request.bank_name %></p>
              </td>
              <td class="py-4 px-4">
                <div>
                  <p class="text-white font-mono"><%= request.account_number %></p>
                  <p class="text-gray-400 text-sm"><%= request.account_name %></p>
                </div>
              </td>
              <td class="py-4 px-4">
                <% if (request.status === 'pending') { %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-500/30">
                    <i class="ti ti-clock mr-1"></i>
                    <%= t('admin_referral.status.pending') %>
                  </span>
                <% } else if (request.status === 'approved') { %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/20 text-green-400 border border-green-500/30">
                    <i class="ti ti-check mr-1"></i>
                    <%= t('admin_referral.status.approved') %>
                  </span>
                <% } else if (request.status === 'rejected') { %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-500/30">
                    <i class="ti ti-x mr-1"></i>
                    <%= t('admin_referral.status.rejected') %>
                  </span>
                <% } %>
              </td>
              <td class="py-4 px-4">
                <div>
                  <p class="text-white text-sm"><%= new Date(request.requested_at).toLocaleDateString('id-ID') %></p>
                  <p class="text-gray-400 text-xs"><%= new Date(request.requested_at).toLocaleTimeString('id-ID') %></p>
                </div>
              </td>
              <td class="py-4 px-4">
                <% if (request.status === 'pending') { %>
                  <div class="flex space-x-2">
                    <button
                      onclick="processWithdrawal('<%= request.id %>', 'approve')"
                      class="btn-success text-xs px-3 py-1"
                      title="<%= t('admin_referral.approve') %>"
                    >
                      <i class="ti ti-check"></i>
                    </button>
                    <button
                      onclick="processWithdrawal('<%= request.id %>', 'reject')"
                      class="btn-danger text-xs px-3 py-1"
                      title="<%= t('admin_referral.reject') %>"
                    >
                      <i class="ti ti-x"></i>
                    </button>
                  </div>
                <% } else { %>
                  <div class="text-gray-500 text-sm">
                    <% if (request.processed_at) { %>
                      <%= t('admin_referral.processed') %>: <%= new Date(request.processed_at).toLocaleDateString('id-ID') %>
                    <% } %>
                    <% if (request.admin_notes) { %>
                      <br><span class="text-xs"><%= t('admin_referral.note') %>: <%= request.admin_notes %></span>
                    <% } %>
                  </div>
                <% } %>
              </td>
            </tr>
          <% }); %>
        </tbody>
      </table>
    </div>
  <% } else { %>
    <div class="text-center py-12">
      <i class="ti ti-cash text-gray-600 text-4xl mb-4"></i>
      <p class="text-gray-400 text-lg"><%= t('admin_referral.no_withdrawal_requests') %></p>
      <p class="text-gray-500 text-sm mt-2"><%= t('admin_referral.withdrawal_requests_appear') %></p>
    </div>
  <% } %>
</div>

<!-- Process Modal -->
<div id="processModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg p-6 w-full max-w-md">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-white" id="modalTitle"><%= t('admin_referral.process_withdrawal') %></h3>
        <button onclick="closeProcessModal()" class="text-gray-400 hover:text-white">
          <i class="ti ti-x text-xl"></i>
        </button>
      </div>

      <form id="processForm" onsubmit="submitProcess(event)">
        <input type="hidden" id="withdrawalId" name="withdrawalId">
        <input type="hidden" id="action" name="action">

        <div class="mb-4">
          <label class="block text-gray-300 text-sm font-medium mb-2"><%= t('admin_referral.admin_notes') %></label>
          <textarea
            name="notes"
            rows="3"
            class="w-full bg-dark-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-primary focus:outline-none"
            placeholder="<%= t('admin_referral.admin_notes_placeholder') %>"
          ></textarea>
        </div>

        <div class="flex space-x-3">
          <button type="button" onclick="closeProcessModal()" class="flex-1 btn-secondary" id="cancelBtn">
            <%= t('admin_referral.cancel') %>
          </button>
          <button type="submit" class="flex-1" id="submitBtn">
            <span id="submitBtnText">Process</span>
            <i id="submitBtnIcon" class="hidden ml-2"></i>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function refreshPage() {
  window.location.reload();
}

function filterRequests() {
  const filter = document.getElementById('statusFilter').value;
  const rows = document.querySelectorAll('.withdrawal-row');
  
  rows.forEach(row => {
    const status = row.getAttribute('data-status');
    if (filter === '' || status === filter) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

function processWithdrawal(id, action) {
  document.getElementById('withdrawalId').value = id;
  document.getElementById('action').value = action;
  
  const modal = document.getElementById('processModal');
  const title = document.getElementById('modalTitle');
  const submitBtn = document.getElementById('submitBtn');
  
  if (action === 'approve') {
    title.textContent = '<%= t("admin_referral.approve_withdrawal") %>';
    document.getElementById('submitBtnText').textContent = '<%= t("admin_referral.approve") %>';
    submitBtn.className = 'flex-1 btn-success';
  } else {
    title.textContent = '<%= t("admin_referral.reject_withdrawal") %>';
    document.getElementById('submitBtnText').textContent = '<%= t("admin_referral.reject") %>';
    submitBtn.className = 'flex-1 btn-danger';
  }
  
  modal.classList.remove('hidden');
}

function closeProcessModal() {
  document.getElementById('processModal').classList.add('hidden');
  document.getElementById('processForm').reset();
}

async function submitProcess(event) {
  event.preventDefault();

  // Show loading state
  const submitBtn = document.getElementById('submitBtn');
  const submitBtnText = document.getElementById('submitBtnText');
  const submitBtnIcon = document.getElementById('submitBtnIcon');
  const cancelBtn = document.getElementById('cancelBtn');

  const originalText = submitBtnText.textContent;

  submitBtn.disabled = true;
  cancelBtn.disabled = true;
  submitBtnText.textContent = '<%= t("admin_referral.processing_request") %>';
  submitBtnIcon.className = 'ti ti-loader-2 ml-2 animate-spin';
  submitBtnIcon.classList.remove('hidden');

  const formData = new FormData(event.target);
  const data = {
    action: formData.get('action'),
    notes: formData.get('notes')
  };

  const withdrawalId = formData.get('withdrawalId');

  try {
    const response = await fetch(`/admin/settlements/${withdrawalId}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (result.success) {
      // Show success state
      submitBtnText.textContent = '<%= t("referral.success") %>';
      submitBtnIcon.className = 'ti ti-check ml-2';

      showNotification(result.message, 'success');
      closeProcessModal();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } else {
      showNotification(result.error || '<%= t("admin_referral.withdrawal_process_failed") %>', 'error');
    }
  } catch (error) {
    console.error('Process withdrawal error:', error);
    showNotification('<%= t("admin_referral.withdrawal_process_error") %>', 'error');
  } finally {
    // Reset button state after delay
    setTimeout(() => {
      submitBtn.disabled = false;
      cancelBtn.disabled = false;
      submitBtnText.textContent = originalText;
      submitBtnIcon.classList.add('hidden');
    }, 2000);
  }
}

function showNotification(message, type) {
  // Use the proper notification system
  if (window.showNotification && window.showNotification[type]) {
    window.showNotification[type]('Penarikan Saldo', message);
  } else {
    // Fallback to alert if notification system is not available
    alert(message);
  }
}
</script>

<style>
.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}
</style>
